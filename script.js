// Mobile Menu Toggle
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
    const navMenu = document.querySelector('.nav-menu');
    
    if (mobileMenuToggle && navMenu) {
        mobileMenuToggle.addEventListener('click', function() {
            navMenu.classList.toggle('active');
        });
    }

    // Smooth scrolling for navigation links
    const navLinks = document.querySelectorAll('a[href^="#"]');
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href');
            const targetSection = document.querySelector(targetId);
            
            if (targetSection) {
                const headerHeight = document.querySelector('.header').offsetHeight;
                const targetPosition = targetSection.offsetTop - headerHeight;
                
                window.scrollTo({
                    top: targetPosition,
                    behavior: 'smooth'
                });
            }
        });
    });

    // Initialize floating WhatsApp button
    initializeWhatsAppButton();

    // WhatsApp contact buttons (both nav and floating)
    const whatsappBtn = document.getElementById('whatsappBtn');
    const whatsappFloatBtn = document.getElementById('whatsappFloatBtn');

    [whatsappBtn, whatsappFloatBtn].forEach(btn => {
        if (btn) {
            // Set the WhatsApp link using the configured number and message
            const message = encodeURIComponent(window.WHATSAPP_MESSAGE || 'Halo! Saya tertarik untuk bergabung dengan SwagBucks.');
            const phoneNumber = window.WHATSAPP_NUMBER || '6281234567890';
            btn.setAttribute('href', `https://wa.me/${phoneNumber}?text=${message}`);

            btn.addEventListener('click', function(e) {
                // Track the click for analytics if needed
                console.log('WhatsApp contact button clicked');
            });
        }
    });

    // Scroll animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // Observe elements for animation
    const animatedElements = document.querySelectorAll('.step-card, .testimonial-card, .merchant-card, .stat-item');
    animatedElements.forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(30px)';
        el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(el);
    });

    // Counter animation for stats
    const statNumbers = document.querySelectorAll('.stat-item h3');
    const statsObserver = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                animateCounter(entry.target);
                statsObserver.unobserve(entry.target);
            }
        });
    }, { threshold: 0.5 });

    statNumbers.forEach(stat => {
        statsObserver.observe(stat);
    });

    // Header scroll effect
    let lastScrollTop = 0;
    const header = document.querySelector('.header');
    
    window.addEventListener('scroll', function() {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        
        if (scrollTop > lastScrollTop && scrollTop > 100) {
            // Scrolling down
            header.style.transform = 'translateY(-100%)';
        } else {
            // Scrolling up
            header.style.transform = 'translateY(0)';
        }
        
        lastScrollTop = scrollTop;
    });
});

// Helper functions
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function showMessage(message, type) {
    // Remove existing messages
    const existingMessage = document.querySelector('.message');
    if (existingMessage) {
        existingMessage.remove();
    }
    
    // Create message element
    const messageEl = document.createElement('div');
    messageEl.className = `message message-${type}`;
    messageEl.textContent = message;
    
    // Style the message
    messageEl.style.cssText = `
        position: fixed;
        top: 100px;
        right: 20px;
        padding: 15px 20px;
        border-radius: 8px;
        color: white;
        font-weight: 600;
        z-index: 10000;
        max-width: 300px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        transform: translateX(100%);
        transition: transform 0.3s ease;
    `;
    
    // Set background color based on type
    switch(type) {
        case 'success':
            messageEl.style.backgroundColor = '#4CAF50';
            break;
        case 'error':
            messageEl.style.backgroundColor = '#f44336';
            break;
        case 'info':
            messageEl.style.backgroundColor = '#2196F3';
            break;
        default:
            messageEl.style.backgroundColor = '#666';
    }
    
    // Add to page
    document.body.appendChild(messageEl);
    
    // Animate in
    setTimeout(() => {
        messageEl.style.transform = 'translateX(0)';
    }, 100);
    
    // Remove after 5 seconds
    setTimeout(() => {
        messageEl.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (messageEl.parentNode) {
                messageEl.parentNode.removeChild(messageEl);
            }
        }, 300);
    }, 5000);
}

function animateCounter(element) {
    const text = element.textContent;
    const hasRp = text.includes('Rp');
    const hasPlus = text.includes('+');
    const numberMatch = text.match(/[\d,\.]+/);
    
    if (!numberMatch) return;
    
    const finalNumber = parseFloat(numberMatch[0].replace(/[,\.]/g, ''));
    const duration = 2000; // 2 seconds
    const steps = 60;
    const increment = finalNumber / steps;
    let current = 0;
    
    const timer = setInterval(() => {
        current += increment;
        if (current >= finalNumber) {
            current = finalNumber;
            clearInterval(timer);
        }
        
        let displayNumber;
        if (finalNumber >= 1000000) {
            displayNumber = (current / 1000000).toFixed(1) + ' Miliar';
        } else if (finalNumber >= 1000) {
            displayNumber = Math.floor(current).toLocaleString('id-ID');
        } else {
            displayNumber = Math.floor(current);
        }
        
        if (hasRp) {
            element.textContent = 'Rp ' + displayNumber;
        } else if (hasPlus) {
            element.textContent = displayNumber + '+';
        } else {
            element.textContent = displayNumber;
        }
    }, duration / steps);
}

// Initialize WhatsApp buttons
function initializeWhatsAppButton() {
    const message = encodeURIComponent(window.WHATSAPP_MESSAGE || 'Halo! Saya tertarik untuk bergabung dengan SwagBucks.');
    const phoneNumber = window.WHATSAPP_NUMBER || '6281234567890';
    const whatsappUrl = `https://wa.me/${phoneNumber}?text=${message}`;

    // Update all WhatsApp buttons
    const whatsappButtons = [
        document.getElementById('whatsappBtn'),
        document.getElementById('whatsappFloatBtn')
    ];

    whatsappButtons.forEach(btn => {
        if (btn) {
            btn.setAttribute('href', whatsappUrl);
        }
    });
}

// Show/hide floating WhatsApp button based on scroll
function handleWhatsAppFloatVisibility() {
    const whatsappFloat = document.getElementById('whatsappFloat');
    if (!whatsappFloat) return;

    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

    if (scrollTop > 300) {
        whatsappFloat.style.opacity = '1';
        whatsappFloat.style.visibility = 'visible';
    } else {
        whatsappFloat.style.opacity = '0.7';
        whatsappFloat.style.visibility = 'visible';
    }
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    initializeWhatsAppButton();

    // Add scroll listener for floating button visibility
    window.addEventListener('scroll', handleWhatsAppFloatVisibility);

    // Initial visibility check
    handleWhatsAppFloatVisibility();
});
