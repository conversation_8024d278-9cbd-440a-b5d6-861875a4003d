/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    background: #fff;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
}

.navbar {
    padding: 1rem 0;
}

.navbar .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo-container {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.logo-icon {
    font-size: 2rem;
    color: #4CAF50;
}

.logo-text {
    font-size: 1.5rem;
    font-weight: 700;
    color: #333;
    background: linear-gradient(135deg, #4CAF50, #45a049);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.nav-menu {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-link:hover {
    color: #4CAF50;
}

.mobile-menu-toggle {
    display: none;
    font-size: 1.5rem;
    cursor: pointer;
}

/* Button Styles */
.btn {
    display: inline-block;
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 16px;
}

.btn-primary {
    background: linear-gradient(135deg, #4CAF50, #45a049);
    color: white;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #45a049, #3d8b40);
    transform: translateY(-2px);
}

.btn-secondary {
    background: #fff;
    color: #4CAF50;
    border: 2px solid #4CAF50;
}

.btn-secondary:hover {
    background: #4CAF50;
    color: white;
}

.btn-outline {
    background: transparent;
    color: #4CAF50;
    border: 2px solid #4CAF50;
}

.btn-outline:hover {
    background: #4CAF50;
    color: white;
}

.btn-full {
    width: 100%;
}

.btn-large {
    padding: 16px 32px;
    font-size: 18px;
}

.btn-google {
    background: #db4437;
    color: white;
    width: 100%;
    margin-bottom: 10px;
}

.btn-google:hover {
    background: #c23321;
}

.btn-facebook {
    background: #3b5998;
    color: white;
    width: 100%;
}

.btn-facebook:hover {
    background: #2d4373;
}

.btn-whatsapp {
    background: #25D366;
    color: white;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-whatsapp:hover {
    background: #128C7E;
    color: white;
    text-decoration: none;
}

.btn-whatsapp i {
    font-size: 1.2rem;
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 120px 0 80px;
    min-height: 100vh;
    display: flex;
    align-items: center;
}

.hero-content-center {
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
}

.hero-text-center {
    max-width: 800px;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.hero-subtitle {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.stats-highlight {
    background: rgba(255,255,255,0.1);
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    margin-bottom: 2rem;
    backdrop-filter: blur(10px);
}

.currency {
    font-size: 2rem;
    font-weight: 600;
}

.amount {
    font-size: 3rem;
    font-weight: 700;
    display: block;
    margin: 0.5rem 0;
}

.hero-features {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.feature-item i {
    color: #4CAF50;
    font-size: 1.2rem;
}

.hero-cta {
    margin-top: 3rem;
}

.cta-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
    margin-bottom: 1.5rem;
}

.btn-whatsapp-hero {
    background: linear-gradient(135deg, #25D366, #128C7E);
    color: white;
    animation: glow 2s infinite alternate;
    position: relative;
    overflow: hidden;
}

.btn-whatsapp-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s;
}

.btn-whatsapp-hero:hover::before {
    left: 100%;
}

.btn-whatsapp-hero:hover {
    background: linear-gradient(135deg, #128C7E, #25D366);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(37, 211, 102, 0.4);
}

.btn-whatsapp-hero i {
    margin-right: 0.5rem;
    font-size: 1.2rem;
}

.hero-cta-text {
    margin-top: 1rem;
    font-size: 1.1rem;
    opacity: 0.9;
    font-weight: 500;
}

@keyframes glow {
    0% {
        box-shadow: 0 4px 20px rgba(37, 211, 102, 0.3);
    }
    100% {
        box-shadow: 0 4px 30px rgba(37, 211, 102, 0.6);
    }
}

/* Signup Card */
.signup-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    color: #333;
}

.signup-card h3 {
    text-align: center;
    margin-bottom: 1.5rem;
    color: #333;
    font-size: 1.5rem;
}

.form-group {
    margin-bottom: 1rem;
}

.form-input {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s ease;
}

.form-input:focus {
    outline: none;
    border-color: #4CAF50;
}

.checkbox-group {
    margin: 1.5rem 0;
}

.checkbox-label {
    display: flex;
    align-items: flex-start;
    gap: 0.5rem;
    font-size: 14px;
    line-height: 1.4;
    cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
    margin: 0;
}

.checkbox-label a {
    color: #4CAF50;
    text-decoration: none;
}

.checkbox-label a:hover {
    text-decoration: underline;
}

.divider {
    text-align: center;
    margin: 1.5rem 0;
    position: relative;
}

.divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #e1e5e9;
}

.divider span {
    background: white;
    padding: 0 1rem;
    color: #666;
}

.login-link {
    text-align: center;
    margin-top: 1rem;
    font-size: 14px;
}

.login-link a {
    color: #4CAF50;
    text-decoration: none;
}

.login-link a:hover {
    text-decoration: underline;
}

/* Section Styles */
.section-title {
    font-size: 2.5rem;
    text-align: center;
    margin-bottom: 1rem;
    color: #333;
}

.section-subtitle {
    font-size: 1.1rem;
    text-align: center;
    margin-bottom: 3rem;
    color: #666;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

/* How It Works Section */
.how-it-works {
    padding: 80px 0;
    background: white;
}

.steps-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.step-card {
    background: #f8f9fa;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    transition: transform 0.3s ease;
}

.step-card:hover {
    transform: translateY(-5px);
}

.step-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #4CAF50, #45a049);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: white;
    font-size: 2rem;
}

.step-card h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #333;
}

.step-card p {
    color: #666;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

/* Stats Section */
.stats-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 60px 0;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    text-align: center;
}

.stat-item h3 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.stat-item p {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* Merchants Section */
.merchants-section {
    padding: 80px 0;
    background: white;
}

.merchants-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
}

.merchant-card {
    background: #f8f9fa;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    transition: transform 0.3s ease;
    border: 2px solid transparent;
}

.merchant-card:hover {
    transform: translateY(-5px);
    border-color: #4CAF50;
}

.merchant-card img {
    width: 120px;
    height: 60px;
    object-fit: contain;
    margin-bottom: 1rem;
    filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));
    transition: transform 0.3s ease;
}

.merchant-card:hover img {
    transform: scale(1.05);
}

.merchant-card p {
    color: #4CAF50;
    font-weight: 600;
    font-size: 14px;
}

/* Testimonials Section */
.testimonials {
    padding: 80px 0;
    background: #f8f9fa;
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.testimonial-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.testimonial-card:hover {
    transform: translateY(-5px);
}

.stars {
    color: #ffc107;
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

.testimonial-card p {
    color: #666;
    font-style: italic;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.testimonial-author strong {
    color: #333;
    display: block;
    margin-bottom: 0.25rem;
}

.testimonial-author span {
    color: #999;
    font-size: 14px;
}

/* CTA Section */
.cta-section {
    background: linear-gradient(135deg, #4CAF50, #45a049);
    color: white;
    padding: 60px 0;
    text-align: center;
}

.cta-section h2 {
    font-size: 2.5rem;
    margin-bottom: 2rem;
}

/* Footer */
.footer {
    background: #333;
    color: white;
    padding: 60px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h4 {
    font-size: 1.2rem;
    margin-bottom: 1rem;
    color: #4CAF50;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: #ccc;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: #4CAF50;
}

.footer-bottom {
    border-top: 1px solid #555;
    padding-top: 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-links a {
    color: #ccc;
    font-size: 1.5rem;
    transition: color 0.3s ease;
}

.social-links a:hover {
    color: #4CAF50;
}

/* Responsive Design */
@media (max-width: 768px) {
    .mobile-menu-toggle {
        display: block;
    }

    .nav-menu {
        display: none;
    }

    .nav-menu.active {
        display: flex;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        flex-direction: column;
        padding: 1rem;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        gap: 1rem;
        z-index: 999;
    }

    .hero-content-center {
        padding: 2rem 0;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .steps-grid {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .merchants-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .testimonials-grid {
        grid-template-columns: 1fr;
    }

    .footer-content {
        grid-template-columns: repeat(2, 1fr);
    }

    .footer-bottom {
        flex-direction: column;
        text-align: center;
    }

    .btn-whatsapp {
        justify-content: center;
    }

    /* Make WhatsApp float button extremely prominent on mobile */
    .whatsapp-float {
        bottom: 30px;
        right: 20px;
        animation: bounce 2s infinite, shake 4s infinite;
    }

    .whatsapp-float a {
        width: 90px;
        height: 90px;
        font-size: 3rem;
        border: 6px solid rgba(255, 255, 255, 0.5);
        box-shadow: 0 10px 40px rgba(37, 211, 102, 0.8);
    }

    .whatsapp-tooltip {
        display: block;
        opacity: 1;
        visibility: visible;
        right: 100px;
        font-size: 18px;
        padding: 15px 25px;
        background: linear-gradient(135deg, #FF6B35, #F7931E);
        animation: pulse-text 2s infinite;
    }

    .whatsapp-tooltip::after {
        border-left-color: #FF6B35;
    }

    /* Hero CTA buttons on mobile */
    .cta-buttons {
        flex-direction: column;
        gap: 1.5rem;
    }

    .btn-whatsapp-hero {
        animation: glow 1.5s infinite alternate, bounce 3s infinite;
        font-size: 1.2rem;
        padding: 18px 36px;
    }
}

@media (max-width: 480px) {
    .hero {
        padding: 100px 0 60px;
    }

    .hero-title {
        font-size: 2rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .merchants-grid {
        grid-template-columns: 1fr;
    }

    .footer-content {
        grid-template-columns: 1fr;
    }
}

/* Animation */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.step-card,
.testimonial-card,
.merchant-card {
    animation: fadeInUp 0.6s ease-out;
}

/* Floating WhatsApp Button - Enhanced for Marketing */
.whatsapp-float {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
    animation: bounce 3s infinite;
}

.whatsapp-float a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #25D366, #128C7E);
    border-radius: 50%;
    color: white;
    text-decoration: none;
    font-size: 2.5rem;
    box-shadow: 0 8px 30px rgba(37, 211, 102, 0.6);
    transition: all 0.3s ease;
    position: relative;
    border: 4px solid rgba(255, 255, 255, 0.3);
}

.whatsapp-float a::before {
    content: '';
    position: absolute;
    top: -8px;
    left: -8px;
    right: -8px;
    bottom: -8px;
    border: 2px solid #25D366;
    border-radius: 50%;
    animation: ripple 2s infinite;
}

.whatsapp-float a::after {
    content: '';
    position: absolute;
    top: -15px;
    left: -15px;
    right: -15px;
    bottom: -15px;
    border: 2px solid rgba(37, 211, 102, 0.3);
    border-radius: 50%;
    animation: ripple 2s infinite 0.5s;
}

.whatsapp-float a:hover {
    background: linear-gradient(135deg, #128C7E, #25D366);
    transform: scale(1.15);
    box-shadow: 0 12px 40px rgba(37, 211, 102, 0.8);
}

.whatsapp-tooltip {
    position: absolute;
    right: 90px;
    top: 50%;
    transform: translateY(-50%);
    background: linear-gradient(135deg, #25D366, #128C7E);
    color: white;
    padding: 12px 20px;
    border-radius: 25px;
    font-size: 16px;
    font-weight: 600;
    white-space: nowrap;
    opacity: 1;
    visibility: visible;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(37, 211, 102, 0.4);
    animation: slideIn 1s ease-out;
}

.whatsapp-tooltip::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 100%;
    transform: translateY(-50%);
    border: 8px solid transparent;
    border-left-color: #25D366;
}

.whatsapp-float:hover .whatsapp-tooltip {
    transform: translateY(-50%) scale(1.05);
    box-shadow: 0 6px 20px rgba(37, 211, 102, 0.6);
}

/* Enhanced Animations */
@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

@keyframes ripple {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    100% {
        transform: scale(1.5);
        opacity: 0;
    }
}

@keyframes slideIn {
    0% {
        opacity: 0;
        transform: translateY(-50%) translateX(20px);
    }
    100% {
        opacity: 1;
        transform: translateY(-50%) translateX(0);
    }
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-3px); }
    20%, 40%, 60%, 80% { transform: translateX(3px); }
}

@keyframes pulse-text {
    0%, 100% {
        transform: translateY(-50%) scale(1);
        background: linear-gradient(135deg, #FF6B35, #F7931E);
    }
    50% {
        transform: translateY(-50%) scale(1.05);
        background: linear-gradient(135deg, #F7931E, #FF6B35);
    }
}

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}
